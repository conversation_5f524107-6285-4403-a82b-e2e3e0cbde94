'use client'
import { useState, useEffect } from 'react'
import { SVGIcons, IIcons } from '@/components/_common/icon'
import { isToday, isTomorrow } from 'date-fns'
import ButtonPrimary from '@/components/_common/ButtonPrimary'
import { usePathname, useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { useBooking } from '@/context/useBookingCounseling'
import Image from 'next/image'

// Define types
interface BreakdownAvailability {
  durationInMinute: number
  price: number
  schedule?: ScheduleItem[]
}

interface ScheduleItem {
  date: string
  timezone: string
  times: TimeSlot[]
}

interface TimeSlot {
  time: string
  dateTimeWithTimezone: string
  isAvailable: boolean
}

interface Psychologist {
  id: string
  fullName: string
  profilePhoto?: string
  nickname?: string
  specialization?: string[]
  breakdownAvailability?: BreakdownAvailability[]
  problemCategory?: Array<{ problemCategory: string }>
}

interface FormattedDate {
  day: string
  date: string
  month: string
  fullDate: string
}

export interface BookingModalProps {
  isOpen: boolean
  onClose: () => void
  user: any
  psychologist: Psychologist
  availabilityDate: string[] | null
}

export default function BookingModal({
  isOpen,
  onClose,
  psychologist,
  availabilityDate,
  user,
}: BookingModalProps) {
  const [selectedDuration, setSelectedDuration] = useState<number>(60)
  const [selectedDate, setSelectedDate] = useState('')
  const [selectedTime, setSelectedTime] = useState('')
  const [formattedDates, setFormattedDates] = useState<FormattedDate[]>([])
  const [availableTimes, setAvailableTimes] = useState<string[]>([])
  const [selectedMedia, setSelectedMedia] = useState<'Call' | 'VideoCall'>('VideoCall')
  const router = useRouter()
  const pathname = usePathname()
  const { bookingState, setBookingDetails } = useBooking()

  // Extract problem categories from psychologist data
  const specializations = psychologist?.problemCategory?.map((item) => item.problemCategory) || []

  useEffect(() => {
    if (availabilityDate && availabilityDate.length > 0) {
      const days = ['Minggu', 'Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu']
      const months = ['Jan', 'Feb', 'Mar', 'Apr', 'Mei', 'Jun', 'Jul', 'Agu', 'Sep', 'Okt', 'Nov', 'Des']

      const formatted = availabilityDate.map((dateStr: string, index: number) => {
        const date = new Date(dateStr)
        const day = date.getDay()
        const dayName =
          index === 0 && isToday(date) ? 'Hari ini' : index === 0 && isTomorrow(date) ? 'Besok' : days[day]

        return {
          day: dayName,
          date: date.getDate().toString(),
          month: months[date.getMonth()],
          fullDate: dateStr,
        }
      })

      setFormattedDates(formatted)

      if (bookingState?.selectedDate) {
        const matchingDate = formatted.find((d) => d.fullDate.startsWith(bookingState.selectedDate))
        if (matchingDate) {
          setSelectedDate(matchingDate.fullDate)
        } else if (formatted.length > 0) {
          setSelectedDate(formatted[0].fullDate)
        }
      } else if (formatted.length > 0) {
        setSelectedDate(formatted[0].fullDate)
      }
    }
  }, [availabilityDate, bookingState])

  // Effect for available times
  useEffect(() => {
    if (selectedDate && psychologist.breakdownAvailability) {
      const selectedDurationSchedule = psychologist.breakdownAvailability.find(
        (breakdown) => breakdown.durationInMinute === selectedDuration
      )

      if (selectedDurationSchedule?.schedule) {
        const scheduleForDate = selectedDurationSchedule.schedule.find(
          (schedule) => schedule.date === selectedDate.split('T')[0]
        )

        if (scheduleForDate?.times) {
          const availableTimeSlots = scheduleForDate.times
            .filter((slot) => slot.isAvailable)
            .map((slot) => `${slot.time} WIB`)

          setAvailableTimes(availableTimeSlots)

          if (!availableTimeSlots.find((time) => time.startsWith(selectedTime))) {
            setSelectedTime(availableTimeSlots[0]?.split(' ')[0] || '')
          }
        } else {
          setAvailableTimes([])
          setSelectedTime('')
        }
      } else {
        setAvailableTimes([])
        setSelectedTime('')
      }
    } else {
      setAvailableTimes([])
      setSelectedTime('')
    }
  }, [selectedDate, selectedDuration, psychologist.breakdownAvailability])

  // Set duration from booking state if available
  useEffect(() => {
    if (bookingState?.duration) {
      setSelectedDuration(bookingState.duration)
    }
  }, [bookingState])

  // Helper function to calculate end time
  const getEndTime = (startTime: string, durationMinutes: number) => {
    const [hours, minutes] = startTime.split(':').map(Number)

    let endMinutes = minutes + durationMinutes
    let endHours = hours + Math.floor(endMinutes / 60)
    endMinutes = endMinutes % 60

    return `${endHours.toString().padStart(2, '0')}:${endMinutes.toString().padStart(2, '0')}`
  }

  const handleBooking = () => {
    if (!user) {
      return router.push('/auth/login')
    }

    // Format the date and time for display
    const selectedDateObj = new Date(selectedDate)
    const dayNames = ['Minggu', 'Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu']
    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'Mei', 'Jun', 'Jul', 'Agu', 'Sep', 'Okt', 'Nov', 'Des']

    const durationInMinutes =
      typeof selectedDuration === 'string' ? parseInt(selectedDuration, 10) : selectedDuration

    const formattedDate = `${dayNames[selectedDateObj.getDay()]}, ${selectedDateObj.getDate()} ${
      monthNames[selectedDateObj.getMonth()]
    } ${selectedDateObj.getFullYear()}, ${selectedTime}-${getEndTime(selectedTime, durationInMinutes)} WIB (GMT+7)`

    // Calculate price based on selected duration
    let price = 149000
    if (psychologist.breakdownAvailability && psychologist.breakdownAvailability.length > 0) {
      const selectedOption = psychologist.breakdownAvailability.find(
        (option) => option.durationInMinute === durationInMinutes
      )
      if (selectedOption) {
        price = selectedOption.price
      }
    } else if (durationInMinutes === 120) {
      price = 299000
    }

    const discount = 0
    const voucherPromo = 0
    const totalAfterDiscount = price - discount - voucherPromo
    const finalPrice = totalAfterDiscount

    const formattedSpecializations =
      specializations.length <= 3
        ? specializations.join(', ')
        : `${specializations.slice(0, 3).join(', ')}, +${specializations.length - 3} lainnya`

    const [timeHours, timeMinutes] = selectedTime.split(':').map(Number)
    const scheduleDate = new Date(selectedDate)
    scheduleDate.setHours(timeHours, timeMinutes, 0, 0)
    const rawSchedule = scheduleDate.toISOString()

    setBookingDetails({
      psychologistId: psychologist.id || '',
      psychologistName: psychologist.fullName,
      psychologistImage: psychologist.profilePhoto || '',
      specializations: formattedSpecializations,
      selectedDate,
      selectedTime,
      formattedDate,
      rawSchedule: rawSchedule,
      duration: durationInMinutes,
      method: selectedMedia,
      location: 'Online',
      price,
      discount,
      voucherPromo,
      totalAfterDiscount,
      finalPrice,
    })

    router.push(`${pathname}/konseling`)
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-end">
      <div className="bg-white w-full max-h-[90vh] rounded-t-2xl overflow-y-auto">
        {/* Header */}
        <div className="sticky top-0 bg-white border-b border-gray-200 p-4 flex items-center justify-between">
          <h2 className="text-lg font-semibold">Jadwal Konseling</h2>
          <Button onClick={onClose} variant="ghost" size="icon" className="w-8 h-8 rounded-full">
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </Button>
        </div>

        {/* Psychologist Info */}
        <div className="p-4 border-b border-gray-100">
          <div className="flex items-center gap-3">
            <div className="relative w-12 h-12 rounded-full overflow-hidden">
              {psychologist.profilePhoto ? (
                <Image
                  width={48}
                  height={48}
                  src={psychologist.profilePhoto}
                  alt={psychologist.fullName}
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="w-full h-full bg-gradient-to-r from-main-100 to-purple-300"></div>
              )}
            </div>
            <div>
              <div className="flex items-center gap-2">
                <h3 className="font-semibold text-base">{psychologist.fullName}</h3>
                <svg className="w-4 h-4 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                  <path
                    fillRule="evenodd"
                    d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
              <p className="text-sm text-gray-600">Psikolog Klinis</p>
              <div className="flex flex-wrap gap-1 mt-1">
                {specializations.slice(0, 4).map((spec, index) => (
                  <span key={index} className="text-xs bg-gray-100 px-2 py-1 rounded-full">
                    {spec}
                  </span>
                ))}
                {specializations.length > 4 && (
                  <span className="text-xs text-gray-500">+{specializations.length - 4} lainnya</span>
                )}
              </div>
            </div>
          </div>
        </div>

        <div className="p-4">
          {/* Duration Options */}
          <h3 className="font-semibold mb-3">Durasi Konseling</h3>
          <div className="flex gap-2 mb-6">
            {psychologist.breakdownAvailability &&
              psychologist.breakdownAvailability.map((breakdown, index) => (
                <button
                  key={index}
                  className={`flex-1 py-3 px-3 border rounded-lg text-center ${
                    selectedDuration === breakdown.durationInMinute
                      ? 'border-main-500 bg-main-50 text-main-500'
                      : 'border-gray-200'
                  }`}
                  onClick={() => setSelectedDuration(breakdown.durationInMinute)}
                >
                  <div className="font-semibold">{breakdown.durationInMinute} Menit</div>
                  <div className="text-sm text-gray-500">Rp{breakdown.price.toLocaleString('id-ID')}</div>
                </button>
              ))}
          </div>

          {/* Date Selection */}
          <h3 className="font-semibold mb-3">Tanggal</h3>
          <div className="flex gap-2 mb-6 overflow-x-auto pb-2">
            {formattedDates.map((date) => (
              <button
                key={date.fullDate}
                className={`flex flex-col items-center min-w-16 border rounded-lg p-2 ${
                  selectedDate === date.fullDate ? 'border-main-500 bg-main-50' : 'border-gray-200'
                }`}
                onClick={() => setSelectedDate(date.fullDate)}
              >
                <span
                  className={`text-xs font-medium ${
                    selectedDate === date.fullDate ? 'text-main-500' : 'text-gray-600'
                  }`}
                >
                  {date.day}
                </span>
                <span className="text-lg font-bold">{date.date}</span>
                <span className="text-xs text-gray-500">{date.month}</span>
              </button>
            ))}
            <button className="border border-gray-200 rounded-lg p-2 flex-shrink-0 flex items-center justify-center min-w-16">
              <SVGIcons name={IIcons.Calendar} className="text-gray-400 w-5 h-5" />
            </button>
          </div>

          {/* Time Selection */}
          <h3 className="font-semibold mb-3">Waktu</h3>
          <div className="grid grid-cols-3 gap-2 mb-6">
            {availableTimes.length > 0 ? (
              availableTimes.map((time) => (
                <button
                  key={time}
                  className={`py-2 px-3 border rounded-lg text-center text-sm font-medium ${
                    selectedTime === time.split(' ')[0]
                      ? 'border-main-500 bg-main-50 text-main-500'
                      : 'border-gray-200'
                  }`}
                  onClick={() => setSelectedTime(time.split(' ')[0])}
                >
                  {time}
                </button>
              ))
            ) : (
              <div className="col-span-3 text-center text-sm text-gray-500 py-4">
                {selectedDate
                  ? 'Tidak ada jadwal tersedia untuk durasi yang dipilih'
                  : 'Silakan pilih tanggal terlebih dahulu'}
              </div>
            )}
          </div>

          {/* Media Selection */}
          <h3 className="font-semibold mb-3">Media Konseling</h3>
          <div className="flex gap-2 mb-6">
            <button
              className={`flex-1 flex items-center justify-center gap-2 py-3 px-4 border rounded-lg ${
                selectedMedia === 'Call' ? 'border-main-500 text-main-500 bg-main-50' : 'border-gray-200'
              }`}
              onClick={() => setSelectedMedia('Call')}
            >
              <SVGIcons name={IIcons.Call} className="text-main-500 w-5 h-5" />
              <div className="flex flex-col items-start">
                <span className="text-sm font-semibold">Voice Call</span>
                <span className="text-xs text-gray-500">Via GoogleMeet</span>
              </div>
            </button>
            <button
              className={`flex-1 flex items-center justify-center gap-2 py-3 px-4 border rounded-lg ${
                selectedMedia === 'VideoCall' ? 'border-main-500 text-main-500 bg-main-50' : 'border-gray-200'
              }`}
              onClick={() => setSelectedMedia('VideoCall')}
            >
              <SVGIcons name={IIcons.Video} className="text-main-500 w-5 h-5" />
              <div className="flex flex-col items-start">
                <span className="text-sm font-semibold">Video Call</span>
                <span className="text-xs text-gray-500">Via GoogleMeet</span>
              </div>
            </button>
          </div>

          {/* Book Button */}
          <ButtonPrimary
            variant="contained"
            className="w-full py-3 rounded-lg font-semibold hover:bg-main-600 transition-colors"
            onClick={handleBooking}
            disabled={!selectedTime}
          >
            Buat Jadwal
          </ButtonPrimary>
        </div>
      </div>
    </div>
  )
}
