'use client'

import { <PERSON><PERSON>, DialogContent, DialogTitle } from '@/components/ui/dialog'
import { Checkbox } from '@/components/ui/checkbox'
import { Button } from '@/components/ui/button'
import { useState } from 'react'
import { AppModal } from '@/components/_common/Modal/AppModal'
import ButtonPrimary from '@/components/_common/ButtonPrimary'

interface FilterPopupProps {
  open: boolean
  onClose: () => void
  onApplyFilter: (specializations: string[], experience: string) => void
}

const SPECIALIZATIONS = [
  'Per<PERSON><PERSON>an',
  '<PERSON><PERSON><PERSON><PERSON>an',
  '<PERSON><PERSON><PERSON><PERSON>',
  '<PERSON>didi<PERSON>',
  '<PERSON><PERSON><PERSON><PERSON>',
  '<PERSON><PERSON>',
  'Depresi',
  '<PERSON><PERSON><PERSON> & <PERSON>',
  '<PERSON>rauma',
  '<PERSON><PERSON><PERSON><PERSON> khus<PERSON>',
  '<PERSON><PERSON> dan <PERSON>',
]

const EXPERIENCE_RANGES = [
  { label: '0-2 tahun', value: '0_TO_2' },
  { label: '2-5 tahun', value: '2_TO_5' },
  { label: 'Lebih dari 5 tahun', value: 'GREATER_5' },
]

export const FilterExAndSpecPopup = ({ open, onClose, onApplyFilter }: FilterPopupProps) => {
  const [selectedSpecializations, setSelectedSpecializations] = useState<string[]>([])
  const [selectedExperience, setSelectedExperience] = useState<string[]>([])

  const toggleSelection = (arr: string[], value: string, setFunc: (val: string[]) => void) => {
    setFunc(arr.includes(value) ? arr.filter((v) => v !== value) : [...arr, value])
  }

  const handleApply = () => {
    const joinedExperience = selectedExperience.join(',') // Gabung string pengalaman
    onApplyFilter(selectedSpecializations, joinedExperience)
    onClose()
  }

  return (
    <AppModal open={open} onClose={onClose} title="Filter Psikolog">
      <DialogTitle>ddd</DialogTitle>
      <div className="space-y-6 px-4 pb-6">
        <div>
          <label className="text-lg font-semibold">Bidang Psikolog</label>
          <div className="mt-3 grid grid-cols-2 gap-3">
            {SPECIALIZATIONS.map((spec) => (
              <div key={spec} className="flex items-center space-x-2">
                <Checkbox
                  checked={selectedSpecializations.includes(spec)}
                  onCheckedChange={() =>
                    toggleSelection(selectedSpecializations, spec, setSelectedSpecializations)
                  }
                />
                <label>{spec}</label>
              </div>
            ))}
          </div>
        </div>

        <div>
          <label className="text-lg font-semibold">Pengalaman</label>
          <div className="mt-3 space-y-2">
            {EXPERIENCE_RANGES.map((exp) => (
              <div key={exp.value} className="flex items-center space-x-2">
                <Checkbox
                  checked={selectedExperience.includes(exp.value)}
                  onCheckedChange={() =>
                    toggleSelection(selectedExperience, exp.value, setSelectedExperience)
                  }
                />
                <label>{exp.label}</label>
              </div>
            ))}
          </div>
        </div>

        <div className="flex justify-end space-x-2">
          <ButtonPrimary variant="outlined" onClick={onClose}>
            Batal
          </ButtonPrimary>
          <ButtonPrimary variant="contained" onClick={handleApply}>
            Terapkan Filter
          </ButtonPrimary>
        </div>
      </div>
    </AppModal>
  )
}
